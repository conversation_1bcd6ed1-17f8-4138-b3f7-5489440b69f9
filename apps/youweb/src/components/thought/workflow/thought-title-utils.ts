import type { Editor } from '@tiptap/core';
import { throttle } from 'lodash-es';
import type { RefObject } from 'react';
import { ThoughtTitleTypeEnum } from '@/typings/thought';
import { apiClient, callAPI } from '@/utils/callHTTP';
import type { ThoughtBodyComponentRef } from '../type';
import { ThoughtContentUtils } from './thought-content-utils';

export type ThoughtTitleUtilsOptions = {
  id?: string;
  editor: Editor;
  thoughtComponentRef: RefObject<ThoughtBodyComponentRef>;
};

interface ThoughtAITitleLocalStorageData {
  id: string;
  contentLength: number;
  lastGenTimestamp?: number;
}

const THOUGHT_TITLE_CHANNEL = 'thought-title-channel-';

const TITLE_CHANGE_DATA_TYPE = 'thought-title-change';

const THOUGHT_UPDATE_DATA_THROTTLE_TIME = 100;

export const STORAGE_PREFIX = '$youmind_thought_gen_ai_title_';

export const STORAGE_IDS_KEY = '$youmind_thought_gen_ai_title_ids';

export const MAX_STORED_IDS = 100;

export const MIN_GEN_TITLE_CONTENT_LENGTH = 30;

export const MAX_GEN_TITLE_CONTENT_LENGTH = 3000;

export const UPDATE_TITLE_LENGTH_THRESHOLD = 150;

export const LIMIT_GEN_TITLE_INTERVAL = 2 * 60 * 1000;

export class ThoughtTitleUtils {
  private id?: string;
  private editor: Editor;
  private thoughtComponentRef: RefObject<ThoughtBodyComponentRef>;
  private channel: BroadcastChannel;

  constructor(options: ThoughtTitleUtilsOptions) {
    this.id = options.id;
    this.editor = options.editor;
    this.thoughtComponentRef = options.thoughtComponentRef;
    this.channel = new BroadcastChannel(THOUGHT_TITLE_CHANNEL + options.id);
    this.channel.addEventListener('message', this.handleTitleUpdateByChannel);
  }

  broadcastTitleChange(title: string) {
    this.channel.postMessage({
      type: TITLE_CHANGE_DATA_TYPE,
      id: this.id,
      title,
    });
  }

  private getTitleFromLocalStorage(): ThoughtAITitleLocalStorageData | null {
    const data = localStorage.getItem(`${STORAGE_PREFIX}${this.id}`);
    if (!data) return null;
    return JSON.parse(data) as ThoughtAITitleLocalStorageData;
  }

  private updateStoredIds(): void {
    try {
      const storedIdsJson = localStorage.getItem(STORAGE_IDS_KEY) || '[]';
      const storedIds = JSON.parse(storedIdsJson) as string[];

      const filteredIds = storedIds.filter((id) => id !== this.id);

      if (this.id) {
        filteredIds.push(this.id);
      }

      if (filteredIds.length > MAX_STORED_IDS) {
        const removeCount = filteredIds.length - MAX_STORED_IDS;
        const idsToRemove = filteredIds.slice(0, removeCount);

        idsToRemove.forEach((id) => {
          localStorage.removeItem(`${STORAGE_PREFIX}${id}`);
        });

        const updatedIds = filteredIds.slice(removeCount);
        localStorage.setItem(STORAGE_IDS_KEY, JSON.stringify(updatedIds));
      } else {
        localStorage.setItem(STORAGE_IDS_KEY, JSON.stringify(filteredIds));
      }
    } catch (error) {
      console.error('Failed to update stored IDs:', error);
    }
  }

  private saveToLocalStorage(contentLength: number): void {
    if (!this.id) return;
    try {
      const titleData: ThoughtAITitleLocalStorageData = {
        id: this.id,
        contentLength,
        lastGenTimestamp: Date.now(),
      };
      localStorage.setItem(`${STORAGE_PREFIX}${this.id}`, JSON.stringify(titleData));
      this.updateStoredIds();
    } catch (error) {
      console.error('Failed to save title data to localStorage:', error);
    }
  }

  shouldGenAITitle(): boolean {
    const titleType = this.thoughtComponent?.getTitleType();
    if (titleType === ThoughtTitleTypeEnum.manual) {
      return false;
    }
    const genTitleContent = ThoughtContentUtils.getGenAITitleContent(this.editor.state.doc);
    if (genTitleContent.length < MIN_GEN_TITLE_CONTENT_LENGTH) {
      return false;
    }
    const localData = this.getTitleFromLocalStorage();
    // 如果本地没有 localData 则保存一下当前的长度，但是不进行生成
    if (!localData) {
      const curTitle = this.thoughtComponent?.getTitle();
      this.saveToLocalStorage(genTitleContent.length);
      // 如果当前标题为空，则进行生成，否则不进行生成
      return !curTitle;
    }

    // 检查是否过了2分钟的冷却时间
    const now = Date.now();
    const timeSinceLastGen = now - (localData.lastGenTimestamp || 0);

    if (timeSinceLastGen < LIMIT_GEN_TITLE_INTERVAL) {
      return false;
    }

    const isNeedGen =
      Math.abs(genTitleContent.length - localData.contentLength) >= UPDATE_TITLE_LENGTH_THRESHOLD;
    // 如果需要生成，则保存当前的长度
    if (isNeedGen) {
      this.saveToLocalStorage(genTitleContent.length);
    }
    return isNeedGen;
  }

  async getAITitle({
    content,
    useCache = true,
  }: {
    content: string;
    useCache?: boolean;
  }): Promise<string> {
    let materialContent = content;
    if (content.length > MAX_GEN_TITLE_CONTENT_LENGTH) {
      const startIndex = content.length - MAX_GEN_TITLE_CONTENT_LENGTH;
      materialContent = content.slice(startIndex);
    }
    this.thoughtComponent?.setIsGenTitle(true);
    const { data } = await callAPI(
      apiClient.thoughtApi.genTitle({
        content: materialContent,
        use_cache: useCache,
      }),
    );
    // const { data } = await callHTTP<{ title: string }>('/api/v1/thought/genTitle', {
    //   method: 'POST',
    //   body: { content: materialContent, useCache },
    // });
    this.thoughtComponent?.setIsGenTitle(false);
    return data?.title || '';
  }

  private handleTitleUpdateByChannel = throttle((event: MessageEvent) => {
    if (!this.id) return;
    if (event.data.type === TITLE_CHANGE_DATA_TYPE && event.data.id === this.id) {
      this.thoughtComponent?.onUpdate?.({
        title: event.data.title,
        id: this.id,
      });
      this.thoughtComponent?.setTitle(event.data.title);
    }
  }, THOUGHT_UPDATE_DATA_THROTTLE_TIME);

  get thoughtComponent() {
    return this.thoughtComponentRef.current;
  }

  get title() {
    return this.thoughtComponent?.getTitle();
  }

  destroy() {
    this.channel.close();
  }
}
